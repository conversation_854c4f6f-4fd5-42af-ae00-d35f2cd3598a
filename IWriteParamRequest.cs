// Copyright (c) USR. All rights reserved.

using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using CommonPkgs.Protocol.Px.V1_0;

namespace CommonPkgs.Protocol.Px;

public interface IWriteParamRequest
{
    [JsonPropertyName("VER")]
    public string Ver { get; }
}

public interface IWriteParamResponse
{
    [JsonPropertyName("VER")]
    public string Ver { get; set; }

}

public class WriteParamResponseCommand
{
    [JsonPropertyName("CMDTYPE")]
    public string CmdType { get; set; } = default!;

    [JsonPropertyName("RESULT")]
    public string Result { get; set; } = default!;
}

public interface IWriteParamRequestCommand
{
    [JsonPropertyName("CMDTYPE")]
    public string Cmdtype { get; }

}
