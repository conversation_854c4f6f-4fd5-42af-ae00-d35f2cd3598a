// Copyright (c) USR. All rights reserved.

using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Json.Serialization.Metadata;

namespace CommonPkgs.Protocol.Px.V1_0;

public class WriteParamResponse : IWriteParamResponse
{
    [JsonPropertyName("VER")]
    public string Ver { get; set; } = default!;

    [JsonPropertyName("MSG")]
    public string Msg { get; set; } = default!;

    [JsonPropertyName("ERR")]
    public int Err { get; set; } = default!;

    [JsonPropertyName("TYPE")]
    public string Type { get; set; } = default!;

    [JsonPropertyName("CMD")]
    public WriteParamResponseCommand Cmd { get; set; } = default!;

    [JsonPropertyName("MAC")]
    public string? Mac { get; set; } = default!;

}

public class WriteParamRequest
{
    [JsonPropertyName("VER")]
    public string Ver => "1.0";

    [JsonPropertyName("MSG")]
    public string Msg => "SETPARA";

    [JsonPropertyName("TYPE")]
    public string Type => "JSON";

    [JsonPropertyName("CMD")]
    public IWriteParamRequestCommand Cmd { get; set; } = default!;

    [JsonPropertyName("USER")]
    public string? User { get; set; } = default!;

    [JsonPropertyName("PSW")]
    public string? Psw { get; set; } = default!;

    [JsonPropertyName("MAC")]
    public string? Mac { get; set; } = default!;

}

public class WriteIpParamRequest : IWriteParamRequestCommand
{
    [JsonPropertyName("CMDTYPE")]
    public string Cmdtype => "IP";

    [JsonPropertyName("TYPE")]
    [JsonInclude]
    public string Type { get; set; } = default!;

    [JsonPropertyName("IP")]
    [JsonInclude]
    public string Ip { get; set; } = default!;

    [JsonPropertyName("SM")]
    [JsonInclude]
    public string Sm { get; set; } = default!;

    [JsonPropertyName("GW")]
    [JsonInclude]
    public string Gw { get; set; } = default!;
}

public class WriteAAAParamRequest : IWriteParamRequestCommand
{
    [JsonPropertyName("CMDTYPE")]
    public string Cmdtype => "AAA";

    [JsonPropertyName("TYPE")]
    [JsonInclude]
    public string AAA { get; set; } = default!;

}