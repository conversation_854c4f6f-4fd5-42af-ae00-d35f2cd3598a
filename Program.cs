using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Json.Serialization.Metadata;
using CommonPkgs.Protocol.Px;
using CommonPkgs.Protocol.Px.V1_0;

var options = new JsonSerializerOptions
{
    WriteIndented = true,
    TypeInfoResolver = new DefaultJsonTypeInfoResolver
    {
        Modifiers =
        {
            static typeInfo =>
            {
                if (typeInfo.Type == typeof(IWriteParamRequestCommand))
                {
                    typeInfo.PolymorphismOptions = new()
                    {
                        DerivedTypes =
                        {
                            new JsonDerivedType(typeof(WriteIpParamRequest), "IP"),
                            new JsonDerivedType(typeof(WriteAAAParamRequest), "AAA")
                        }
                    };
                }
            }
        }
    }
};

var request = new WriteParamRequest
{
    Cmd = new WriteIpParamRequest
    {
        Type = "DHCP",
        Ip = "***********",
        Sm = "*************",
        Gw = "***********"
    },
    User = "admin",
    Psw = "123456",
    Mac = "001122334455"
};

var json = JsonSerializer.Serialize(request, options);
Console.WriteLine(json);

var request2 = new WriteParamRequest
{
    Cmd = new WriteAAAParamRequest
    {
        AAA = "123"
    },
    User = "admin",
    Psw = "123456",
    Mac = "001122334455"
};

Console.WriteLine(JsonSerializer.Serialize(request2, options));